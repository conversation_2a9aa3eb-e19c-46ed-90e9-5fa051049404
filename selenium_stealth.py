#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Selenium防检测公共类
提供各种反检测技术，避免被网站识别为自动化工具
"""

import random
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
import json
import os

class SeleniumStealth:
    """Selenium防检测类"""
    
    def __init__(self):
        # 常用User-Agent列表
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
        ]
        
        # 屏幕分辨率列表
        self.screen_resolutions = [
            (1920, 1080),
            (1366, 768),
            (1440, 900),
            (1536, 864),
            (1280, 720),
            (1600, 900),
            (1024, 768)
        ]
        
        # 语言设置列表
        self.languages = [
            "zh-CN,zh;q=0.9,en;q=0.8",
            "en-US,en;q=0.9",
            "zh-CN,zh;q=0.9",
            "en-GB,en;q=0.9",
            "zh-TW,zh;q=0.9,en;q=0.8"
        ]
        
        # 时区列表
        self.timezones = [
            "Asia/Shanghai",
            "America/New_York",
            "Europe/London",
            "Asia/Tokyo",
            "Australia/Sydney"
        ]
        
    def get_random_user_agent(self):
        """获取随机User-Agent"""
        return random.choice(self.user_agents)
    
    def get_random_screen_resolution(self):
        """获取随机屏幕分辨率"""
        return random.choice(self.screen_resolutions)
    
    def get_random_language(self):
        """获取随机语言设置"""
        return random.choice(self.languages)
    
    def get_random_timezone(self):
        """获取随机时区"""
        return random.choice(self.timezones)
    
    def create_stealth_options(self, headless=False):
        """创建防检测的Chrome选项"""
        options = Options()
        
        # 基础反检测设置
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 随机User-Agent
        user_agent = self.get_random_user_agent()
        options.add_argument(f'--user-agent={user_agent}')
        
        # 随机屏幕分辨率
        width, height = self.get_random_screen_resolution()
        options.add_argument(f'--window-size={width},{height}')
        
        # 语言和时区设置
        language = self.get_random_language()
        timezone = self.get_random_timezone()
        options.add_argument(f'--lang={language}')
        options.add_argument(f'--timezone={timezone}')
        
        # 其他反检测参数
        options.add_argument("--disable-web-security")
        options.add_argument("--disable-features=VizDisplayCompositor")
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-plugins")
        options.add_argument("--disable-images")  # 可选：禁用图片加载提高速度
        options.add_argument("--disable-javascript")  # 可选：禁用JavaScript
        options.add_argument("--disable-css")  # 可选：禁用CSS
        
        # 性能优化
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-software-rasterizer")
        options.add_argument("--disable-background-timer-throttling")
        options.add_argument("--disable-backgrounding-occluded-windows")
        options.add_argument("--disable-renderer-backgrounding")
        
        # 内存优化
        options.add_argument("--memory-pressure-off")
        options.add_argument("--max_old_space_size=4096")
        
        # 网络优化
        options.add_argument("--disable-background-networking")
        options.add_argument("--disable-default-apps")
        options.add_argument("--disable-sync")
        
        # 隐私设置
        options.add_argument("--disable-web-security")
        options.add_argument("--allow-running-insecure-content")
        options.add_argument("--disable-site-isolation-trials")
        
        # 无头模式设置
        if headless:
            options.add_argument("--headless")
            options.add_argument("--disable-gpu")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
        
        return options
    
    def apply_stealth_js(self, driver):
        """应用JavaScript反检测脚本"""
        stealth_js = """
        // 删除webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 修改navigator属性
        const newProto = navigator.__proto__;
        delete newProto.webdriver;
        navigator.__proto__ = newProto;
        
        // 修改plugins
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // 修改languages
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en'],
        });
        
        // 修改permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        
        // 修改chrome对象
        window.chrome = {
            runtime: {},
        };
        
        // 修改permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        
        // 添加随机鼠标移动
        const originalMouseEvent = window.MouseEvent;
        window.MouseEvent = function(type, init) {
            if (init && init.clientX && init.clientY) {
                init.clientX += Math.random() * 2 - 1;
                init.clientY += Math.random() * 2 - 1;
            }
            return new originalMouseEvent(type, init);
        };
        
        // 修改屏幕分辨率检测
        Object.defineProperty(screen, 'width', {
            get: () => %d,
        });
        Object.defineProperty(screen, 'height', {
            get: () => %d,
        });
        Object.defineProperty(screen, 'availWidth', {
            get: () => %d,
        });
        Object.defineProperty(screen, 'availHeight', {
            get: () => %d,
        });
        
        // 修改时区
        Object.defineProperty(Intl, 'DateTimeFormat', {
            get: () => function(locale, options) {
                if (options && options.timeZone) {
                    options.timeZone = '%s';
                }
                return new Intl.DateTimeFormat(locale, options);
            },
        });
        
        // 修改canvas指纹
        const originalGetContext = HTMLCanvasElement.prototype.getContext;
        HTMLCanvasElement.prototype.getContext = function(type, ...args) {
            const context = originalGetContext.call(this, type, ...args);
            if (type === '2d') {
                const originalFillText = context.fillText;
                context.fillText = function(...args) {
                    args[1] += Math.random() * 0.1;
                    args[2] += Math.random() * 0.1;
                    return originalFillText.apply(this, args);
                };
            }
            return context;
        };
        
        // 修改WebGL指纹
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === 37445) {
                return 'Intel Inc.';
            }
            if (parameter === 37446) {
                return 'Intel(R) Iris(TM) Graphics 6100';
            }
            return getParameter.call(this, parameter);
        };
        """ % (
            self.get_random_screen_resolution()[0],
            self.get_random_screen_resolution()[1],
            self.get_random_screen_resolution()[0],
            self.get_random_screen_resolution()[1],
            self.get_random_timezone()
        )
        
        try:
            driver.execute_script(stealth_js)
        except Exception as e:
            print(f"应用反检测脚本时出错: {e}")
    
    def add_random_delays(self, driver, min_delay=0.5, max_delay=2.0):
        """添加随机延迟，模拟人类行为"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def simulate_human_scroll(self, driver, scroll_pause_time=1):
        """模拟人类滚动行为"""
        # 获取页面高度
        last_height = driver.execute_script("return document.body.scrollHeight")
        
        while True:
            # 滚动到页面底部
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            
            # 等待页面加载
            time.sleep(scroll_pause_time)
            
            # 计算新的滚动高度并与上一次的滚动高度进行比较
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height
    
    def simulate_human_typing(self, element, text, min_delay=0.05, max_delay=0.15):
        """模拟人类打字行为"""
        element.clear()
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(min_delay, max_delay))
    
    def simulate_mouse_movement(self, driver, element=None):
        """模拟鼠标移动"""
        actions = ActionChains(driver)
        
        if element:
            # 移动到指定元素
            actions.move_to_element(element)
        else:
            # 随机移动
            x = random.randint(100, 800)
            y = random.randint(100, 600)
            actions.move_by_offset(x, y)
        
        actions.perform()
        time.sleep(random.uniform(0.1, 0.5))
    
    def create_stealth_driver(self, headless=False, proxy=None, stealth=True):
        """创建防检测的WebDriver"""
        if stealth:
            options = self.create_stealth_options(headless)
        else:
            # 创建普通选项
            options = Options()
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument("--window-size=1200,800")
            if headless:
                options.add_argument("--headless")
        
        # 添加代理设置
        if proxy:
            options.add_argument(f'--proxy-server={proxy}')
        
        try:
            driver = webdriver.Chrome(options=options)
            
            # 应用反检测脚本
            if stealth:
                self.apply_stealth_js(driver)
            
            # 设置窗口大小
            if not headless:
                width, height = self.get_random_screen_resolution()
                driver.set_window_size(width, height)
            
            # 设置页面加载超时
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)
            
            return driver
            
        except Exception as e:
            print(f"创建WebDriver失败: {e}")
            return None
    
    def navigate_with_stealth(self, driver, url, wait_time=3):
        """使用防检测技术导航到页面"""
        try:
            # 添加随机延迟
            self.add_random_delays(driver)
            
            # 导航到页面
            driver.get(url)
            
            # 等待页面加载
            time.sleep(wait_time)
            
            # 模拟人类滚动
            self.simulate_human_scroll(driver)
            
            return True
            
        except Exception as e:
            print(f"防检测导航失败: {e}")
            return False
    
    def click_with_stealth(self, driver, element, wait_time=1):
        """使用防检测技术点击元素"""
        try:
            # 模拟鼠标移动
            self.simulate_mouse_movement(driver, element)
            
            # 添加随机延迟
            self.add_random_delays(driver, 0.2, 0.8)
            
            # 点击元素
            element.click()
            
            # 等待页面响应
            time.sleep(wait_time)
            
            return True
            
        except Exception as e:
            print(f"防检测点击失败: {e}")
            return False
    
    def input_with_stealth(self, driver, element, text, wait_time=1):
        """使用防检测技术输入文本"""
        try:
            # 模拟鼠标移动
            self.simulate_mouse_movement(driver, element)
            
            # 添加随机延迟
            self.add_random_delays(driver, 0.3, 1.0)
            
            # 模拟人类打字
            self.simulate_human_typing(element, text)
            
            # 等待输入完成
            time.sleep(wait_time)
            
            return True
            
        except Exception as e:
            print(f"防检测输入失败: {e}")
            return False
    
    def get_page_info(self, driver):
        """获取页面信息，用于调试"""
        try:
            info = {
                'title': driver.title,
                'url': driver.current_url,
                'user_agent': driver.execute_script("return navigator.userAgent"),
                'webdriver': driver.execute_script("return navigator.webdriver"),
                'plugins': driver.execute_script("return navigator.plugins.length"),
                'languages': driver.execute_script("return navigator.languages"),
                'screen_width': driver.execute_script("return screen.width"),
                'screen_height': driver.execute_script("return screen.height"),
                'window_width': driver.execute_script("return window.innerWidth"),
                'window_height': driver.execute_script("return window.innerHeight")
            }
            return info
        except Exception as e:
            print(f"获取页面信息失败: {e}")
            return {}

# 全局实例
stealth_manager = SeleniumStealth() 