#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控制中台系统测试脚本
测试系统的基本功能和连接性
"""

import socket
import threading
import time
import json
import uuid
from datetime import datetime

class SystemTester:
    def __init__(self):
        self.test_results = []
        self.server_host = 'localhost'
        self.server_port = 8888
        
    def log_test(self, test_name, result, message=""):
        """记录测试结果"""
        status = "✅ PASS" if result else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        test_info = f"[{timestamp}] {test_name}: {status}"
        if message:
            test_info += f" - {message}"
        print(test_info)
        self.test_results.append({
            'test': test_name,
            'result': result,
            'message': message,
            'timestamp': timestamp
        })
        
    def test_port_availability(self):
        """测试端口可用性"""
        print("\n🔍 测试端口可用性...")
        
        try:
            # 测试端口是否被占用
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(2)
            result = test_socket.connect_ex((self.server_host, self.server_port))
            test_socket.close()
            
            if result == 0:
                self.log_test("端口可用性", True, f"端口 {self.server_port} 可用")
            else:
                self.log_test("端口可用性", False, f"端口 {self.server_port} 不可用")
                
        except Exception as e:
            self.log_test("端口可用性", False, f"测试失败: {str(e)}")
            
    def test_server_connection(self):
        """测试服务器连接"""
        print("\n🔍 测试服务器连接...")
        
        try:
            # 尝试连接服务器
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(5)
            test_socket.connect((self.server_host, self.server_port))
            
            # 发送测试注册信息
            test_data = {
                'type': 'register',
                'client_id': 'test_client',
                'client_name': 'TestClient'
            }
            test_socket.send(json.dumps(test_data).encode('utf-8'))
            
            # 接收响应
            response = test_socket.recv(1024).decode('utf-8')
            response_data = json.loads(response)
            
            test_socket.close()
            
            if response_data.get('status') == 'success':
                self.log_test("服务器连接", True, "成功连接到服务器")
            else:
                self.log_test("服务器连接", False, "服务器响应异常")
                
        except Exception as e:
            self.log_test("服务器连接", False, f"连接失败: {str(e)}")
            
    def test_message_protocol(self):
        """测试消息协议"""
        print("\n🔍 测试消息协议...")
        
        try:
            # 连接服务器
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(5)
            test_socket.connect((self.server_host, self.server_port))
            
            # 测试注册消息
            register_data = {
                'type': 'register',
                'client_id': 'protocol_test',
                'client_name': 'ProtocolTest'
            }
            test_socket.send(json.dumps(register_data).encode('utf-8'))
            
            # 接收注册确认
            response = test_socket.recv(1024).decode('utf-8')
            response_data = json.loads(response)
            
            if response_data.get('type') == 'register_confirm':
                self.log_test("注册协议", True, "注册消息协议正常")
            else:
                self.log_test("注册协议", False, "注册消息协议异常")
                
            # 测试状态更新消息
            status_data = {
                'type': 'status_update',
                'status': 'online'
            }
            test_socket.send(json.dumps(status_data).encode('utf-8'))
            self.log_test("状态更新协议", True, "状态更新消息发送成功")
            
            test_socket.close()
            
        except Exception as e:
            self.log_test("消息协议", False, f"协议测试失败: {str(e)}")
            
    def test_selenium_availability(self):
        """测试Selenium可用性"""
        print("\n🔍 测试Selenium可用性...")
        
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            # 测试Chrome选项
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            # 尝试启动Chrome
            driver = webdriver.Chrome(options=chrome_options)
            driver.quit()
            
            self.log_test("Selenium可用性", True, "Chrome浏览器可用")
            
        except Exception as e:
            self.log_test("Selenium可用性", False, f"Selenium不可用: {str(e)}")
            
    def test_dependencies(self):
        """测试依赖包"""
        print("\n🔍 测试依赖包...")
        
        required_modules = [
            'tkinter',
            'socket',
            'threading',
            'json',
            'uuid',
            'datetime',
            'selenium'
        ]
        
        for module in required_modules:
            try:
                if module == 'tkinter':
                    import tkinter
                elif module == 'socket':
                    import socket
                elif module == 'threading':
                    import threading
                elif module == 'json':
                    import json
                elif module == 'uuid':
                    import uuid
                elif module == 'datetime':
                    import datetime
                else:
                    __import__(module)
                    
                self.log_test(f"依赖包 {module}", True, "模块可用")
                
            except ImportError:
                self.log_test(f"依赖包 {module}", False, "模块不可用")
                
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 控制中台系统测试")
        print("=" * 50)
        
        # 运行各项测试
        self.test_dependencies()
        self.test_port_availability()
        self.test_server_connection()
        self.test_message_protocol()
        self.test_selenium_availability()
        
        # 输出测试总结
        self.print_test_summary()
        
    def print_test_summary(self):
        """输出测试总结"""
        print("\n" + "=" * 50)
        print("📊 测试总结")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['result'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['result']:
                    print(f"  - {result['test']}: {result['message']}")
                    
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！系统可以正常运行。")
        else:
            print("\n⚠️  部分测试失败，请检查相关配置。")

def main():
    """主函数"""
    tester = SystemTester()
    
    print("选择测试模式:")
    print("1. 运行所有测试")
    print("2. 仅测试依赖包")
    print("3. 仅测试网络连接")
    print("4. 仅测试Selenium")
    print("5. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-5): ").strip()
            
            if choice == '1':
                tester.run_all_tests()
                break
            elif choice == '2':
                tester.test_dependencies()
                break
            elif choice == '3':
                tester.test_port_availability()
                tester.test_server_connection()
                tester.test_message_protocol()
                break
            elif choice == '4':
                tester.test_selenium_availability()
                break
            elif choice == '5':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请输入 1-5")
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main() 