#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控制中台 - 客户端启动脚本
"""

import sys
import os
import subprocess

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def install_requirements():
    """安装依赖包"""
    print("🔍 检查依赖包...")
    
    try:
        import selenium
        import tkinter
        print("✅ 依赖包已安装")
        return True
    except ImportError:
        print("⚠️  缺少依赖包，正在安装...")
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ 依赖包安装完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败")
            return False

def check_chrome():
    """检查Chrome浏览器"""
    print("🔍 检查Chrome浏览器...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.quit()
        print("✅ Chrome浏览器可用")
        return True
    except Exception as e:
        print(f"⚠️  Chrome浏览器不可用: {e}")
        print("   客户端将无法执行浏览器操作，但可以连接到控制端")
        return False

def start_client():
    """启动客户端"""
    print("🚀 启动客户端...")
    
    try:
        # 导入并运行客户端
        from client_platform import ClientPlatform
        client = ClientPlatform()
        client.run()
    except ImportError as e:
        print(f"❌ 导入客户端模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 启动客户端失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🖥️ 控制中台 - 客户端")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 安装依赖包
    if not install_requirements():
        input("按回车键退出...")
        return
    
    # 检查Chrome浏览器
    check_chrome()
    
    # 启动客户端
    print()
    start_client()

if __name__ == "__main__":
    main() 