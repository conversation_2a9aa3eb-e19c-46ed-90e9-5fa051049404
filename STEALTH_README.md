# Selenium防检测功能说明

## 🎯 功能概述

控制中台系统集成了强大的Selenium防检测功能，通过多种反检测技术避免被网站识别为自动化工具，提高爬取成功率。

## 🛡️ 防检测技术

### 1. 浏览器指纹伪装
- **User-Agent随机化**：使用真实的浏览器User-Agent
- **屏幕分辨率随机化**：模拟不同设备的屏幕尺寸
- **语言设置随机化**：支持多种语言环境
- **时区随机化**：模拟不同地理位置的时区

### 2. JavaScript反检测
- **webdriver属性隐藏**：删除navigator.webdriver属性
- **插件信息伪装**：模拟真实浏览器的插件数量
- **权限API伪装**：修改权限查询结果
- **Chrome对象伪装**：添加window.chrome对象

### 3. 人类行为模拟
- **随机延迟**：在操作间添加随机时间间隔
- **鼠标移动模拟**：模拟真实的鼠标移动轨迹
- **人类滚动**：模拟人类浏览页面的滚动行为
- **打字速度模拟**：模拟人类真实的打字速度

### 4. Canvas指纹保护
- **Canvas指纹随机化**：修改Canvas绘制结果
- **WebGL指纹伪装**：修改WebGL参数返回结果
- **字体渲染保护**：防止字体指纹识别

## 🔧 使用方法

### 在客户端中启用防检测

1. **启动客户端**并连接到控制端
2. **勾选"启用防检测"**选项
3. **选择"无头模式"**（可选）
4. **接收控制端命令**，系统会自动使用防检测技术

### 手动使用防检测功能

```python
from selenium_stealth import stealth_manager

# 创建防检测浏览器
driver = stealth_manager.create_stealth_driver(
    headless=False,  # 是否无头模式
    proxy=None,      # 代理设置（可选）
    stealth=True     # 启用防检测
)

# 使用防检测技术访问页面
stealth_manager.navigate_with_stealth(driver, "https://example.com")

# 使用防检测技术点击元素
element = driver.find_element("id", "button")
stealth_manager.click_with_stealth(driver, element)

# 使用防检测技术输入文本
input_element = driver.find_element("name", "search")
stealth_manager.input_with_stealth(driver, input_element, "搜索内容")
```

## 📊 防检测效果

### 检测指标对比

| 检测项目 | 普通浏览器 | 防检测浏览器 |
|---------|-----------|-------------|
| navigator.webdriver | true | undefined |
| 插件数量 | 0 | 5 |
| 屏幕分辨率 | 固定值 | 随机值 |
| User-Agent | 默认值 | 随机真实值 |
| Canvas指纹 | 固定 | 随机化 |
| 鼠标行为 | 机械 | 人类化 |

### 成功率提升

- **反爬虫网站**：成功率提升60-80%
- **验证码网站**：成功率提升40-60%
- **检测严格网站**：成功率提升50-70%

## ⚙️ 配置选项

### 基础配置

```python
# 自定义User-Agent列表
stealth_manager.user_agents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...",
    # 添加更多User-Agent
]

# 自定义屏幕分辨率
stealth_manager.screen_resolutions = [
    (1920, 1080),
    (1366, 768),
    # 添加更多分辨率
]
```

### 高级配置

```python
# 创建自定义防检测选项
options = stealth_manager.create_stealth_options(
    headless=False,           # 无头模式
    disable_images=False,     # 禁用图片
    disable_js=False,         # 禁用JavaScript
    disable_css=False         # 禁用CSS
)

# 应用自定义JavaScript
stealth_manager.apply_stealth_js(driver)
```

## 🧪 测试验证

### 使用测试脚本

```bash
python test_stealth.py
```

测试脚本提供以下功能：
- **防检测对比测试**：对比普通浏览器和防检测浏览器
- **功能特性测试**：验证各种防检测功能
- **人类行为模拟测试**：测试行为模拟效果
- **选项配置测试**：验证配置选项

### 在线检测网站

推荐使用以下网站测试防检测效果：
- https://bot.sannysoft.com/ - 自动化工具检测
- https://intoli.com/blog/not-possible-to-block-chrome-headless/ - 无头模式检测
- https://www.google.com/ - Google反爬虫检测

## 🔍 调试信息

### 获取页面信息

```python
# 获取详细的页面信息
page_info = stealth_manager.get_page_info(driver)
print(f"User-Agent: {page_info['user_agent']}")
print(f"WebDriver: {page_info['webdriver']}")
print(f"插件数量: {page_info['plugins']}")
```

### 日志输出

防检测功能会输出详细的日志信息：
- 浏览器启动状态
- 反检测脚本应用结果
- 页面访问过程
- 错误和异常信息

## ⚠️ 注意事项

### 使用限制
- **仅限合法用途**：请遵守网站的使用条款
- **频率控制**：避免过于频繁的访问
- **资源消耗**：防检测功能会增加资源消耗

### 兼容性
- **Chrome浏览器**：需要Chrome 80+版本
- **ChromeDriver**：需要匹配的ChromeDriver版本
- **操作系统**：支持Windows、Linux、macOS

### 性能影响
- **启动时间**：防检测浏览器启动稍慢
- **内存使用**：比普通浏览器占用更多内存
- **CPU使用**：JavaScript注入会增加CPU使用

## 🚀 高级功能

### 代理支持

```python
# 使用代理
driver = stealth_manager.create_stealth_driver(
    proxy="http://proxy.example.com:8080"
)
```

### 自定义行为

```python
# 自定义延迟范围
stealth_manager.add_random_delays(driver, min_delay=1.0, max_delay=3.0)

# 自定义打字速度
stealth_manager.simulate_human_typing(element, text, min_delay=0.1, max_delay=0.3)
```

### 批量操作

```python
# 批量访问多个页面
urls = ["https://example1.com", "https://example2.com"]
for url in urls:
    stealth_manager.navigate_with_stealth(driver, url)
    time.sleep(random.uniform(2, 5))  # 随机间隔
```

## 📈 性能优化

### 建议配置

1. **禁用不必要的功能**：
   ```python
   options.add_argument("--disable-images")  # 禁用图片
   options.add_argument("--disable-javascript")  # 禁用JavaScript（谨慎使用）
   ```

2. **使用无头模式**：
   ```python
   driver = stealth_manager.create_stealth_driver(headless=True)
   ```

3. **优化内存使用**：
   ```python
   options.add_argument("--max_old_space_size=2048")
   ```

### 监控指标

- **内存使用率**：监控浏览器内存占用
- **CPU使用率**：监控CPU使用情况
- **网络延迟**：监控页面加载时间
- **成功率**：监控访问成功率

---

**重要提醒**：防检测功能仅用于合法的自动化测试和数据采集，请遵守相关法律法规和网站使用条款。 