#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控制中台系统配置文件
管理系统的各种设置和参数
"""

import json
import os
from pathlib import Path

class Config:
    """配置管理类"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.default_config = {
            # 服务器配置
            "server": {
                "host": "0.0.0.0",
                "port": 8888,
                "max_clients": 100,
                "timeout": 30,
                "heartbeat_interval": 5
            },
            
            # 客户端配置
            "client": {
                "default_server_host": "localhost",
                "default_server_port": 8888,
                "heartbeat_interval": 10,
                "reconnect_attempts": 3,
                "reconnect_delay": 5
            },
            
            # 浏览器配置
            "browser": {
                "window_width": 1200,
                "window_height": 800,
                "headless": False,
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "chrome_options": [
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor"
                ]
            },
            
            # 日志配置
            "logging": {
                "level": "INFO",
                "max_log_size": 1024 * 1024,  # 1MB
                "backup_count": 5,
                "log_to_file": True,
                "log_file": "control_center.log"
            },
            
            # 安全配置
            "security": {
                "enable_encryption": False,
                "allowed_ips": [],
                "max_connections_per_ip": 10
            },
            
            # 界面配置
            "ui": {
                "theme": "default",
                "window_width": 1200,
                "window_height": 800,
                "auto_refresh_interval": 5
            }
        }
        
        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置和用户配置
                    return self.merge_config(self.default_config, config)
            else:
                # 创建默认配置文件
                self.save_config(self.default_config)
                return self.default_config
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config
            
    def save_config(self, config=None):
        """保存配置文件"""
        if config is None:
            config = self.config
            
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
            
    def merge_config(self, default, user):
        """合并配置"""
        result = default.copy()
        
        def merge_dict(d1, d2):
            for key, value in d2.items():
                if key in d1 and isinstance(d1[key], dict) and isinstance(value, dict):
                    merge_dict(d1[key], value)
                else:
                    d1[key] = value
                    
        merge_dict(result, user)
        return result
        
    def get(self, key, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key, value):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        # 导航到父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        # 设置值
        config[keys[-1]] = value
        
        # 保存配置
        return self.save_config()
        
    def get_server_config(self):
        """获取服务器配置"""
        return self.config.get('server', {})
        
    def get_client_config(self):
        """获取客户端配置"""
        return self.config.get('client', {})
        
    def get_browser_config(self):
        """获取浏览器配置"""
        return self.config.get('browser', {})
        
    def get_logging_config(self):
        """获取日志配置"""
        return self.config.get('logging', {})
        
    def get_security_config(self):
        """获取安全配置"""
        return self.config.get('security', {})
        
    def get_ui_config(self):
        """获取界面配置"""
        return self.config.get('ui', {})
        
    def update_server_config(self, **kwargs):
        """更新服务器配置"""
        server_config = self.config.get('server', {})
        server_config.update(kwargs)
        self.config['server'] = server_config
        return self.save_config()
        
    def update_client_config(self, **kwargs):
        """更新客户端配置"""
        client_config = self.config.get('client', {})
        client_config.update(kwargs)
        self.config['client'] = client_config
        return self.save_config()
        
    def update_browser_config(self, **kwargs):
        """更新浏览器配置"""
        browser_config = self.config.get('browser', {})
        browser_config.update(kwargs)
        self.config['browser'] = browser_config
        return self.save_config()
        
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.default_config.copy()
        return self.save_config()
        
    def export_config(self, filename):
        """导出配置"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
            
    def import_config(self, filename):
        """导入配置"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.config = self.merge_config(self.default_config, config)
            return self.save_config()
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False

# 全局配置实例
config = Config()

# 便捷函数
def get_config(key, default=None):
    """获取配置值"""
    return config.get(key, default)

def set_config(key, value):
    """设置配置值"""
    return config.set(key, value)

def get_server_config():
    """获取服务器配置"""
    return config.get_server_config()

def get_client_config():
    """获取客户端配置"""
    return config.get_client_config()

def get_browser_config():
    """获取浏览器配置"""
    return config.get_browser_config()

if __name__ == "__main__":
    # 测试配置功能
    print("🔧 配置管理测试")
    print("=" * 40)
    
    # 显示当前配置
    print("服务器配置:")
    print(json.dumps(get_server_config(), indent=2, ensure_ascii=False))
    
    print("\n客户端配置:")
    print(json.dumps(get_client_config(), indent=2, ensure_ascii=False))
    
    print("\n浏览器配置:")
    print(json.dumps(get_browser_config(), indent=2, ensure_ascii=False))
    
    # 测试配置修改
    print("\n测试配置修改...")
    set_config('server.port', 9999)
    print(f"修改后的端口: {get_config('server.port')}")
    
    # 恢复默认配置
    set_config('server.port', 8888)
    print(f"恢复后的端口: {get_config('server.port')}")
    
    print("\n✅ 配置管理测试完成") 