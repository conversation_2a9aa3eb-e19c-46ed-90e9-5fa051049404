@echo off
chcp 65001 >nul
echo ========================================
echo 控制中台 - 客户端启动脚本
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✅ Python已安装
echo.

REM 检查依赖包
echo 🔍 检查依赖包...
python -c "import selenium, tkinter" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少依赖包，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查完成
echo.

REM 检查Chrome浏览器
echo 🔍 检查Chrome浏览器...
python -c "from selenium import webdriver; from selenium.webdriver.chrome.options import Options; options = Options(); options.add_argument('--headless'); driver = webdriver.Chrome(options=options); driver.quit()" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Chrome浏览器不可用，但系统仍可运行
    echo    客户端将无法执行浏览器操作
    echo.
)

echo ✅ 环境检查完成
echo.

REM 启动客户端
echo 🖥️ 启动客户端...
echo.
python client_platform.py

if errorlevel 1 (
    echo.
    echo ❌ 客户端启动失败
    pause
    exit /b 1
)

echo.
echo 👋 客户端已退出
pause 