import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import socket
import threading
import json
import uuid
from datetime import datetime
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import os
from selenium_stealth import stealth_manager

class ClientPlatform:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("控制中台 - 客户端")
        self.root.geometry("800x600")
        
        # 客户端配置
        self.client_id = str(uuid.uuid4())[:8]
        self.server_host = 'localhost'
        self.server_port = 8888
        self.socket = None
        self.connected = False
        self.driver = None
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 连接控制区域
        connection_frame = ttk.LabelFrame(main_frame, text="服务器连接", padding=10)
        connection_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 服务器地址输入
        server_frame = ttk.Frame(connection_frame)
        server_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(server_frame, text="服务器地址:").pack(side=tk.LEFT)
        self.host_entry = ttk.Entry(server_frame, width=20)
        self.host_entry.pack(side=tk.LEFT, padx=(10, 10))
        self.host_entry.insert(0, self.server_host)
        
        ttk.Label(server_frame, text="端口:").pack(side=tk.LEFT)
        self.port_entry = ttk.Entry(server_frame, width=10)
        self.port_entry.pack(side=tk.LEFT, padx=(10, 10))
        self.port_entry.insert(0, str(self.server_port))
        
        # 客户端ID设置
        id_frame = ttk.Frame(connection_frame)
        id_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(id_frame, text="客户端ID:").pack(side=tk.LEFT)
        self.id_entry = ttk.Entry(id_frame, width=20)
        self.id_entry.pack(side=tk.LEFT, padx=(10, 10))
        self.id_entry.insert(0, self.client_id)
        
        # 连接按钮
        btn_frame = ttk.Frame(connection_frame)
        btn_frame.pack(fill=tk.X)
        
        self.connect_btn = ttk.Button(btn_frame, text="连接服务器", command=self.connect_to_server)
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.disconnect_btn = ttk.Button(btn_frame, text="断开连接", command=self.disconnect_from_server, state=tk.DISABLED)
        self.disconnect_btn.pack(side=tk.LEFT)
        
        # 状态显示
        self.status_label = ttk.Label(btn_frame, text="状态: 未连接", foreground="red")
        self.status_label.pack(side=tk.RIGHT)
        
        # 浏览器控制区域
        browser_frame = ttk.LabelFrame(main_frame, text="浏览器控制", padding=10)
        browser_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 防检测设置
        stealth_frame = ttk.Frame(browser_frame)
        stealth_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.stealth_var = tk.BooleanVar(value=True)
        self.stealth_check = ttk.Checkbutton(stealth_frame, text="启用防检测", 
                                           variable=self.stealth_var, state=tk.DISABLED)
        self.stealth_check.pack(side=tk.LEFT)
        
        self.headless_var = tk.BooleanVar(value=False)
        self.headless_check = ttk.Checkbutton(stealth_frame, text="无头模式", 
                                            variable=self.headless_var, state=tk.DISABLED)
        self.headless_check.pack(side=tk.LEFT, padx=(20, 0))
        
        # 浏览器状态
        browser_status_frame = ttk.Frame(browser_frame)
        browser_status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.browser_status_label = ttk.Label(browser_status_frame, text="浏览器状态: 未启动", foreground="red")
        self.browser_status_label.pack(side=tk.LEFT)
        
        self.close_browser_btn = ttk.Button(browser_status_frame, text="关闭浏览器", 
                                          command=self.close_browser, state=tk.DISABLED)
        self.close_browser_btn.pack(side=tk.RIGHT)
        
        # 当前页面信息
        page_frame = ttk.Frame(browser_frame)
        page_frame.pack(fill=tk.X)
        
        ttk.Label(page_frame, text="当前页面:").pack(side=tk.LEFT)
        self.current_url_label = ttk.Label(page_frame, text="无", foreground="gray")
        self.current_url_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="系统日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
    def connect_to_server(self):
        """连接到服务器"""
        try:
            self.server_host = self.host_entry.get().strip()
            self.server_port = int(self.port_entry.get().strip())
            self.client_id = self.id_entry.get().strip()
            
            if not self.server_host or not self.client_id:
                messagebox.showerror("错误", "请输入服务器地址和客户端ID")
                return
                
            # 创建socket连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.server_host, self.server_port))
            
            # 发送注册信息
            register_data = {
                'type': 'register',
                'client_id': self.client_id,
                'client_name': f'Client-{self.client_id}'
            }
            self.socket.send(json.dumps(register_data).encode('utf-8'))
            
            # 接收服务器确认
            response = self.socket.recv(1024).decode('utf-8')
            response_data = json.loads(response)
            
            if response_data.get('status') == 'success':
                self.connected = True
                self.status_label.config(text="状态: 已连接", foreground="green")
                self.connect_btn.config(state=tk.DISABLED)
                self.disconnect_btn.config(state=tk.NORMAL)
                
                # 启用防检测设置
                self.stealth_check.config(state=tk.NORMAL)
                self.headless_check.config(state=tk.NORMAL)
                
                self.log_message(f"成功连接到服务器 {self.server_host}:{self.server_port}")
                self.log_message(f"客户端ID: {self.client_id}")
                
                # 启动消息接收线程
                receive_thread = threading.Thread(target=self.receive_messages, daemon=True)
                receive_thread.start()
                
                # 启动心跳线程
                heartbeat_thread = threading.Thread(target=self.send_heartbeat, daemon=True)
                heartbeat_thread.start()
                
            else:
                self.log_message("服务器拒绝连接")
                self.socket.close()
                
        except Exception as e:
            self.log_message(f"连接服务器失败: {str(e)}")
            messagebox.showerror("错误", f"连接服务器失败: {str(e)}")
            
    def disconnect_from_server(self):
        """断开服务器连接"""
        self.connected = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
            
        self.status_label.config(text="状态: 未连接", foreground="red")
        self.connect_btn.config(state=tk.NORMAL)
        self.disconnect_btn.config(state=tk.DISABLED)
        
        # 禁用防检测设置
        self.stealth_check.config(state=tk.DISABLED)
        self.headless_check.config(state=tk.DISABLED)
        
        self.log_message("已断开服务器连接")
        
    def receive_messages(self):
        """接收服务器消息"""
        while self.connected:
            try:
                data = self.socket.recv(1024).decode('utf-8')
                if not data:
                    break
                    
                message = json.loads(data)
                self.handle_server_message(message)
                
            except Exception as e:
                if self.connected:
                    self.log_message(f"接收消息时出错: {str(e)}")
                break
                
        # 连接断开
        if self.connected:
            self.connected = False
            self.root.after(0, self.handle_disconnection)
            
    def handle_disconnection(self):
        """处理连接断开"""
        self.status_label.config(text="状态: 连接断开", foreground="red")
        self.connect_btn.config(state=tk.NORMAL)
        self.disconnect_btn.config(state=tk.DISABLED)
        self.log_message("服务器连接已断开")
        
    def handle_server_message(self, message):
        """处理服务器消息"""
        msg_type = message.get('type')
        
        if msg_type == 'command':
            operation = message.get('operation')
            
            if operation == 'open_browser':
                url = message.get('url')
                self.root.after(0, lambda: self.execute_open_browser(url))
                
        elif msg_type == 'register_confirm':
            self.log_message("服务器确认注册成功")
            
    def execute_open_browser(self, url):
        """执行打开浏览器命令"""
        try:
            self.log_message(f"收到命令: 打开浏览器访问 {url}")
            
            # 关闭现有浏览器
            if self.driver:
                self.close_browser()
                
            # 启动新浏览器
            self.start_browser()
            
            if self.driver:
                # 检查是否启用防检测
                if self.stealth_var.get():
                    self.log_message("使用防检测技术访问页面...")
                    success = stealth_manager.navigate_with_stealth(self.driver, url)
                    if not success:
                        self.log_message("防检测访问失败，尝试普通访问...")
                        self.driver.get(url)
                else:
                    self.driver.get(url)
                
                self.current_url_label.config(text=url, foreground="blue")
                self.log_message(f"成功打开浏览器并访问: {url}")
                
                # 获取页面信息用于调试
                if self.stealth_var.get():
                    page_info = stealth_manager.get_page_info(self.driver)
                    self.log_message(f"页面信息: {page_info}")
                
                # 发送操作结果
                self.send_operation_result('open_browser', True, f"成功访问 {url}")
            else:
                self.send_operation_result('open_browser', False, "浏览器启动失败")
                
        except Exception as e:
            error_msg = f"打开浏览器失败: {str(e)}"
            self.log_message(error_msg)
            self.send_operation_result('open_browser', False, error_msg)
            
    def start_browser(self):
        """启动浏览器"""
        try:
            # 检查是否启用防检测
            if self.stealth_var.get():
                self.log_message("使用防检测模式启动浏览器...")
                
                # 使用防检测管理器创建浏览器
                headless = self.headless_var.get()
                self.driver = stealth_manager.create_stealth_driver(headless=headless)
                
                if not self.driver:
                    self.log_message("防检测浏览器启动失败，尝试普通模式...")
                    return self.start_browser_normal()
                    
            else:
                return self.start_browser_normal()
                
            self.browser_status_label.config(text="浏览器状态: 运行中", foreground="green")
            self.close_browser_btn.config(state=tk.NORMAL)
            self.log_message("防检测浏览器启动成功")
            return True
            
        except Exception as e:
            self.log_message(f"启动浏览器失败: {str(e)}")
            return False
    
    def start_browser_normal(self):
        """启动普通浏览器"""
        try:
            # Chrome选项配置
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1200,800")
            
            # 尝试启动Chrome
            try:
                self.driver = webdriver.Chrome(options=chrome_options)
            except:
                # 如果Chrome不可用，尝试使用ChromeDriver
                try:
                    service = Service('chromedriver.exe')  # 需要下载chromedriver
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                except Exception as e:
                    self.log_message(f"无法启动Chrome浏览器: {str(e)}")
                    self.log_message("请确保已安装Chrome浏览器或ChromeDriver")
                    return False
                    
            self.browser_status_label.config(text="浏览器状态: 运行中", foreground="green")
            self.close_browser_btn.config(state=tk.NORMAL)
            self.log_message("普通浏览器启动成功")
            return True
            
        except Exception as e:
            self.log_message(f"启动浏览器失败: {str(e)}")
            return False
            
    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
                self.browser_status_label.config(text="浏览器状态: 已关闭", foreground="red")
                self.close_browser_btn.config(state=tk.DISABLED)
                self.current_url_label.config(text="无", foreground="gray")
                self.log_message("浏览器已关闭")
            except Exception as e:
                self.log_message(f"关闭浏览器时出错: {str(e)}")
                
    def send_operation_result(self, operation, success, message):
        """发送操作结果到服务器"""
        if self.connected and self.socket:
            try:
                result_data = {
                    'type': 'operation_result',
                    'operation': operation,
                    'success': success,
                    'message': message
                }
                self.socket.send(json.dumps(result_data).encode('utf-8'))
            except Exception as e:
                self.log_message(f"发送操作结果失败: {str(e)}")
                
    def send_heartbeat(self):
        """发送心跳包"""
        while self.connected:
            try:
                if self.socket:
                    heartbeat_data = {
                        'type': 'status_update',
                        'status': 'online'
                    }
                    self.socket.send(json.dumps(heartbeat_data).encode('utf-8'))
                time.sleep(10)  # 每10秒发送一次心跳
            except:
                break
                
    def run(self):
        """运行客户端"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """窗口关闭时的处理"""
        if self.connected:
            self.disconnect_from_server()
        if self.driver:
            self.close_browser()
        self.root.destroy()

if __name__ == "__main__":
    client = ClientPlatform()
    client.run() 