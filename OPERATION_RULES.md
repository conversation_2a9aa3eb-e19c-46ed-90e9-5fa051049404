# 控制中台操作规则

## 🎯 快速开始

### 启动顺序
1. **先启动控制端** → 点击"启动服务器"
2. **再启动客户端** → 输入服务器地址并连接
3. **选择客户端** → 在控制端选择要控制的客户端
4. **发送命令** → 输入网站地址，点击"打开浏览器访问网站"

## 📋 日常操作规则

### 控制端操作
- ✅ **启动前**：检查网络和端口8888
- ✅ **运行中**：监控客户端状态和系统日志
- ✅ **发送命令**：确认客户端在线后再发送
- ✅ **关闭前**：停止服务器，保存日志

### 客户端操作
- ✅ **连接前**：确认服务器地址和端口正确
- ✅ **运行中**：保持网络连接，响应心跳
- ✅ **执行命令**：及时反馈操作结果
- ✅ **关闭前**：断开连接，关闭浏览器

## 🚫 禁止操作

### 安全禁止
- ❌ 在公网环境使用
- ❌ 向未知客户端发送命令
- ❌ 修改系统核心文件
- ❌ 删除重要日志

### 操作禁止
- ❌ 同时启动多个控制端
- ❌ 使用相同的客户端ID
- ❌ 绕过安全检查
- ❌ 在公共网络使用

## ✅ 推荐操作

### 最佳实践
- ✅ 定期检查系统状态
- ✅ 及时清理离线客户端
- ✅ 记录重要操作步骤
- ✅ 备份配置文件

### 故障处理
- ✅ 检查网络连接
- ✅ 查看系统日志
- ✅ 重启相关服务
- ✅ 联系技术支持

## 📊 状态检查

### 正常状态
- 🟢 控制端：服务器运行中
- 🟢 客户端：状态显示"已连接"
- 🟢 浏览器：状态显示"运行中"
- 🟢 日志：无错误信息

### 异常状态
- 🔴 控制端：服务器启动失败
- 🔴 客户端：状态显示"未连接"
- 🔴 浏览器：状态显示"启动失败"
- 🔴 日志：出现错误信息

## 🔧 常见问题

### 连接问题
**问题**：客户端无法连接控制端
**解决**：
1. 检查网络连接
2. 确认控制端已启动
3. 验证IP地址和端口
4. 检查防火墙设置

### 浏览器问题
**问题**：浏览器无法启动
**解决**：
1. 确认Chrome已安装
2. 检查ChromeDriver
3. 更新Selenium版本
4. 检查系统权限

### 命令问题
**问题**：命令发送失败
**解决**：
1. 确认客户端在线
2. 检查命令格式
3. 查看错误日志
4. 重新发送命令

## 📞 紧急联系

### 技术支持
- **系统问题**：查看README.md文档
- **操作问题**：查看demo.py演示
- **测试问题**：运行test_system.py

### 故障报告
1. 记录错误信息
2. 保存系统日志
3. 描述操作步骤
4. 提供环境信息

---

**记住**：安全第一，操作规范，及时记录，及时报告。 