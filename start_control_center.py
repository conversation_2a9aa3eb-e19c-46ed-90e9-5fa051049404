#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控制中台 - 控制端启动脚本
"""

import sys
import os
import subprocess

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def install_requirements():
    """安装依赖包"""
    print("🔍 检查依赖包...")
    
    try:
        import selenium
        import tkinter
        print("✅ 依赖包已安装")
        return True
    except ImportError:
        print("⚠️  缺少依赖包，正在安装...")
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ 依赖包安装完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败")
            return False

def start_control_center():
    """启动控制端"""
    print("🚀 启动控制端...")
    
    try:
        # 导入并运行控制端
        from control_center import ControlCenter
        control_center = ControlCenter()
        control_center.run()
    except ImportError as e:
        print(f"❌ 导入控制端模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 启动控制端失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎮 控制中台 - 控制端")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 安装依赖包
    if not install_requirements():
        input("按回车键退出...")
        return
    
    # 启动控制端
    print()
    start_control_center()

if __name__ == "__main__":
    main() 