#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Selenium防检测功能测试脚本
测试各种反检测技术的效果
"""

import time
import json
from selenium_stealth import stealth_manager

def test_stealth_detection():
    """测试防检测功能"""
    print("🧪 Selenium防检测功能测试")
    print("=" * 50)
    
    # 测试网站列表（用于检测自动化工具）
    test_sites = [
        "https://bot.sannysoft.com/",  # 专门检测自动化工具的网站
        "https://intoli.com/blog/not-possible-to-block-chrome-headless/chrome-headless-test.html",
        "https://www.google.com/",
        "https://www.baidu.com/"
    ]
    
    print("1. 测试普通浏览器...")
    driver_normal = None
    try:
        driver_normal = stealth_manager.create_stealth_driver(headless=False, stealth=False)
        if driver_normal:
            test_browser_detection(driver_normal, "普通浏览器", test_sites[0])
            driver_normal.quit()
    except Exception as e:
        print(f"普通浏览器测试失败: {e}")
        if driver_normal:
            driver_normal.quit()
    
    print("\n2. 测试防检测浏览器...")
    driver_stealth = None
    try:
        driver_stealth = stealth_manager.create_stealth_driver(headless=False, stealth=True)
        if driver_stealth:
            test_browser_detection(driver_stealth, "防检测浏览器", test_sites[0])
            driver_stealth.quit()
    except Exception as e:
        print(f"防检测浏览器测试失败: {e}")
        if driver_stealth:
            driver_stealth.quit()
    
    print("\n3. 测试无头模式防检测...")
    driver_headless = None
    try:
        driver_headless = stealth_manager.create_stealth_driver(headless=True, stealth=True)
        if driver_headless:
            test_browser_detection(driver_headless, "无头防检测浏览器", test_sites[0])
            driver_headless.quit()
    except Exception as e:
        print(f"无头防检测浏览器测试失败: {e}")
        if driver_headless:
            driver_headless.quit()

def test_browser_detection(driver, browser_type, test_url):
    """测试浏览器检测"""
    print(f"\n🔍 测试 {browser_type}...")
    
    try:
        # 访问测试网站
        print(f"访问测试网站: {test_url}")
        driver.get(test_url)
        time.sleep(5)
        
        # 获取页面信息
        page_info = stealth_manager.get_page_info(driver)
        print(f"页面标题: {page_info.get('title', 'N/A')}")
        print(f"当前URL: {page_info.get('url', 'N/A')}")
        
        # 检查关键检测指标
        check_detection_indicators(driver, browser_type)
        
        # 截图保存（可选）
        try:
            screenshot_path = f"test_{browser_type.replace(' ', '_')}.png"
            driver.save_screenshot(screenshot_path)
            print(f"截图已保存: {screenshot_path}")
        except Exception as e:
            print(f"截图失败: {e}")
            
    except Exception as e:
        print(f"测试失败: {e}")

def check_detection_indicators(driver, browser_type):
    """检查检测指标"""
    print(f"\n📊 {browser_type} 检测指标:")
    
    indicators = {
        'webdriver': 'navigator.webdriver',
        'plugins': 'navigator.plugins.length',
        'languages': 'navigator.languages',
        'chrome': 'window.chrome',
        'permissions': 'navigator.permissions',
        'screen_width': 'screen.width',
        'screen_height': 'screen.height',
        'window_width': 'window.innerWidth',
        'window_height': 'window.innerHeight'
    }
    
    for name, script in indicators.items():
        try:
            result = driver.execute_script(f"return {script}")
            print(f"  {name}: {result}")
        except Exception as e:
            print(f"  {name}: 检测失败 - {e}")

def test_stealth_features():
    """测试防检测功能特性"""
    print("\n🔧 测试防检测功能特性...")
    
    # 测试随机User-Agent
    print("1. 测试随机User-Agent生成:")
    for i in range(3):
        ua = stealth_manager.get_random_user_agent()
        print(f"   User-Agent {i+1}: {ua}")
    
    # 测试随机屏幕分辨率
    print("\n2. 测试随机屏幕分辨率:")
    for i in range(3):
        res = stealth_manager.get_random_screen_resolution()
        print(f"   分辨率 {i+1}: {res[0]}x{res[1]}")
    
    # 测试随机语言设置
    print("\n3. 测试随机语言设置:")
    for i in range(3):
        lang = stealth_manager.get_random_language()
        print(f"   语言 {i+1}: {lang}")
    
    # 测试随机时区
    print("\n4. 测试随机时区:")
    for i in range(3):
        tz = stealth_manager.get_random_timezone()
        print(f"   时区 {i+1}: {tz}")

def test_human_behavior_simulation():
    """测试人类行为模拟"""
    print("\n👤 测试人类行为模拟...")
    
    try:
        driver = stealth_manager.create_stealth_driver(headless=False)
        if not driver:
            print("无法创建浏览器，跳过人类行为测试")
            return
        
        # 访问一个测试页面
        driver.get("https://www.google.com")
        time.sleep(2)
        
        # 测试随机延迟
        print("1. 测试随机延迟...")
        stealth_manager.add_random_delays(driver, 1, 2)
        print("   随机延迟测试完成")
        
        # 测试鼠标移动
        print("2. 测试鼠标移动...")
        stealth_manager.simulate_mouse_movement(driver)
        print("   鼠标移动测试完成")
        
        # 测试人类滚动
        print("3. 测试人类滚动...")
        stealth_manager.simulate_human_scroll(driver, 1)
        print("   人类滚动测试完成")
        
        # 测试文本输入（如果找到搜索框）
        try:
            search_box = driver.find_element("name", "q")
            print("4. 测试人类打字...")
            stealth_manager.simulate_human_typing(search_box, "test automation")
            print("   人类打字测试完成")
        except:
            print("4. 未找到搜索框，跳过打字测试")
        
        driver.quit()
        
    except Exception as e:
        print(f"人类行为模拟测试失败: {e}")

def test_stealth_options():
    """测试防检测选项"""
    print("\n⚙️ 测试防检测选项...")
    
    # 测试创建选项
    options = stealth_manager.create_stealth_options(headless=False)
    print("1. 防检测选项创建成功")
    
    # 显示部分选项
    print("2. 部分选项内容:")
    for arg in options.arguments[:10]:  # 只显示前10个选项
        print(f"   {arg}")
    
    if len(options.arguments) > 10:
        print(f"   ... 还有 {len(options.arguments) - 10} 个选项")

def main():
    """主函数"""
    print("🎯 Selenium防检测功能测试程序")
    print("=" * 60)
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 完整测试 (所有功能)")
    print("2. 防检测对比测试")
    print("3. 功能特性测试")
    print("4. 人类行为模拟测试")
    print("5. 选项配置测试")
    print("6. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-6): ").strip()
            
            if choice == '1':
                print("\n🚀 开始完整测试...")
                test_stealth_features()
                test_stealth_options()
                test_human_behavior_simulation()
                test_stealth_detection()
                break
            elif choice == '2':
                print("\n🔍 开始防检测对比测试...")
                test_stealth_detection()
                break
            elif choice == '3':
                print("\n🔧 开始功能特性测试...")
                test_stealth_features()
                break
            elif choice == '4':
                print("\n👤 开始人类行为模拟测试...")
                test_human_behavior_simulation()
                break
            elif choice == '5':
                print("\n⚙️ 开始选项配置测试...")
                test_stealth_options()
                break
            elif choice == '6':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请输入 1-6")
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    main() 