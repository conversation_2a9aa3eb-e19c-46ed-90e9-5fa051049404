# 控制中台系统

一个基于Python的控制中台系统，包含控制端和客户端，支持远程控制多个客户端的浏览器操作。

## 功能特性

### 控制端功能
- 🖥️ 图形化界面，易于操作
- 🌐 支持多客户端连接管理
- 📊 实时显示客户端状态（在线/离线）
- 🎯 支持向多个客户端发送操作指令
- 📝 完整的系统日志记录
- 🔄 自动监控客户端连接状态

### 客户端功能
- 🖥️ 图形化界面
- 🔗 自动连接到控制端
- 🌐 支持Selenium浏览器控制
- 📡 实时状态反馈
- 🔄 心跳保活机制

## 系统架构

```
控制端 (control_center.py)
    ↓ Socket通信
客户端1 (client_platform.py) ←→ Selenium浏览器
客户端2 (client_platform.py) ←→ Selenium浏览器
客户端N (client_platform.py) ←→ Selenium浏览器
```

## 安装要求

### 系统要求
- Python 3.7+
- Windows/Linux/macOS
- Chrome浏览器（客户端需要）

### Python依赖
```bash
pip install -r requirements.txt
```

### 主要依赖包
- `selenium`: 浏览器自动化控制
- `tkinter`: GUI界面（Python内置）
- `socket`: 网络通信（Python内置）
- `threading`: 多线程支持（Python内置）

## 使用方法

### 1. 启动控制端
```bash
python control_center.py
```

控制端界面说明：
- **服务器控制**: 启动/停止服务器
- **客户端操作**: 输入目标网站，选择客户端发送命令
- **客户端列表**: 显示所有连接的客户端及其状态
- **系统日志**: 显示操作日志和状态信息

### 2. 启动客户端
```bash
python client_platform.py
```

客户端界面说明：
- **服务器连接**: 配置服务器地址和客户端ID
- **浏览器控制**: 显示浏览器状态和当前页面
- **系统日志**: 显示连接和操作日志

### 3. 操作流程
1. 先启动控制端，点击"启动服务器"
2. 启动一个或多个客户端，输入控制端IP地址，点击"连接服务器"
3. 在控制端选择要控制的客户端（可多选）
4. 输入要访问的网站地址，点击"打开浏览器访问网站"
5. 客户端会自动打开浏览器并访问指定网站

## 配置说明

### 控制端配置
- 默认监听端口: 8888
- 客户端超时时间: 30秒
- 心跳检查间隔: 5秒

### 客户端配置
- 默认服务器地址: localhost
- 默认端口: 8888
- 心跳发送间隔: 10秒
- 浏览器窗口大小: 1200x800

## 网络配置

### 局域网使用
1. 控制端和客户端需要在同一局域网内
2. 控制端使用 `0.0.0.0` 监听所有网络接口
3. 客户端连接时使用控制端的局域网IP地址

### 防火墙设置
确保端口8888在防火墙中开放：
- Windows: 在Windows防火墙中添加例外规则
- Linux: 使用 `ufw allow 8888` 或 `iptables` 配置

## 故障排除

### 常见问题

1. **客户端无法连接控制端**
   - 检查网络连接
   - 确认控制端已启动
   - 检查防火墙设置
   - 验证IP地址和端口号

2. **浏览器无法启动**
   - 确保已安装Chrome浏览器
   - 下载对应版本的ChromeDriver
   - 检查Selenium版本兼容性

3. **客户端显示离线**
   - 检查网络连接稳定性
   - 确认心跳机制正常工作
   - 检查服务器监听状态

### 日志分析
系统提供详细的日志记录，可以通过日志信息定位问题：
- 连接建立/断开日志
- 命令发送/接收日志
- 浏览器操作结果日志
- 错误和异常信息

## 扩展功能

### 可扩展的操作类型
当前支持的操作：
- `open_browser`: 打开浏览器访问指定网站

可以扩展的操作：
- 页面元素点击
- 表单填写
- 页面截图
- 数据提取
- 页面导航

### 自定义命令
可以通过修改代码添加新的命令类型：
1. 在控制端添加新的命令按钮
2. 在客户端添加对应的命令处理函数
3. 实现具体的操作逻辑

## 安全注意事项

1. **网络安全**: 当前版本未加密，建议在内网环境使用
2. **权限控制**: 控制端可以完全控制客户端，请谨慎使用
3. **浏览器安全**: Selenium操作可能触发网站安全机制
4. **资源管理**: 及时关闭不需要的浏览器实例

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的浏览器控制功能
- 实现多客户端管理
- 提供完整的GUI界面 