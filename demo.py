#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控制中台系统演示脚本
演示如何启动控制端和客户端，以及基本操作流程
"""

import subprocess
import time
import threading
import sys
import os

def run_control_center():
    """启动控制端"""
    print("🚀 启动控制端...")
    try:
        subprocess.run([sys.executable, "control_center.py"], check=True)
    except KeyboardInterrupt:
        print("控制端已停止")
    except Exception as e:
        print(f"启动控制端失败: {e}")

def run_client(client_id):
    """启动客户端"""
    print(f"🖥️ 启动客户端 {client_id}...")
    try:
        # 设置环境变量来标识客户端
        env = os.environ.copy()
        env['CLIENT_ID'] = client_id
        subprocess.run([sys.executable, "client_platform.py"], env=env, check=True)
    except KeyboardInterrupt:
        print(f"客户端 {client_id} 已停止")
    except Exception as e:
        print(f"启动客户端 {client_id} 失败: {e}")

def demo_workflow():
    """演示完整的工作流程"""
    print("=" * 60)
    print("🎯 控制中台系统演示")
    print("=" * 60)
    print()
    print("📋 演示步骤:")
    print("1. 启动控制端服务器")
    print("2. 启动多个客户端")
    print("3. 客户端自动连接到控制端")
    print("4. 控制端向客户端发送浏览器操作命令")
    print("5. 客户端执行浏览器操作")
    print()
    
    # 启动控制端
    control_thread = threading.Thread(target=run_control_center, daemon=True)
    control_thread.start()
    
    # 等待控制端启动
    print("⏳ 等待控制端启动...")
    time.sleep(3)
    
    # 启动多个客户端
    client_threads = []
    for i in range(3):
        client_id = f"demo_client_{i+1}"
        client_thread = threading.Thread(target=run_client, args=(client_id,), daemon=True)
        client_threads.append(client_thread)
        client_thread.start()
        time.sleep(1)  # 间隔启动客户端
    
    print()
    print("✅ 演示环境已启动!")
    print()
    print("📝 操作说明:")
    print("- 在控制端界面点击'启动服务器'")
    print("- 在客户端界面输入服务器地址(默认localhost)并连接")
    print("- 在控制端选择客户端，输入网站地址，点击'打开浏览器访问网站'")
    print("- 观察客户端的浏览器操作")
    print()
    print("🛑 按 Ctrl+C 停止演示")
    
    try:
        # 保持演示运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 演示结束")

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'selenium',
        'tkinter',
        'socket',
        'threading',
        'json',
        'uuid',
        'datetime'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'socket':
                import socket
            elif package == 'threading':
                import threading
            elif package == 'json':
                import json
            elif package == 'uuid':
                import uuid
            elif package == 'datetime':
                import datetime
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("✅ 所有依赖包已安装")
        return True

def check_chrome():
    """检查Chrome浏览器"""
    print("🔍 检查Chrome浏览器...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无头模式测试
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.quit()
        print("✅ Chrome浏览器可用")
        return True
    except Exception as e:
        print(f"❌ Chrome浏览器不可用: {e}")
        print("请确保已安装Chrome浏览器")
        return False

def main():
    """主函数"""
    print("🎮 控制中台系统演示程序")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查Chrome浏览器
    if not check_chrome():
        print("⚠️  Chrome浏览器不可用，但系统仍可运行")
        print("   客户端将无法执行浏览器操作")
    
    print()
    
    # 选择演示模式
    print("请选择演示模式:")
    print("1. 完整演示 (启动控制端 + 3个客户端)")
    print("2. 仅启动控制端")
    print("3. 仅启动客户端")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                demo_workflow()
                break
            elif choice == '2':
                print("🚀 启动控制端...")
                run_control_center()
                break
            elif choice == '3':
                client_id = input("请输入客户端ID (默认: demo_client): ").strip()
                if not client_id:
                    client_id = "demo_client"
                run_client(client_id)
                break
            elif choice == '4':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请输入 1-4")
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main() 