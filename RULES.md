# 控制中台系统使用规则

## 📋 系统概述

控制中台系统是一个基于Python的远程控制平台，支持一个控制端管理多个客户端，通过Selenium进行浏览器自动化操作。本规则文档定义了系统的使用规范、安全要求和操作指南。

## 🛡️ 安全规则

### 1. 网络安全
- **仅限内网使用**：系统设计用于内网环境，禁止在公网环境使用
- **防火墙配置**：确保端口8888在防火墙中正确配置
- **IP白名单**：建议配置允许连接的IP地址白名单
- **加密传输**：当前版本未加密，敏感操作需谨慎

### 2. 权限控制
- **管理员权限**：只有授权人员可以启动控制端
- **客户端限制**：限制每个IP的最大连接数（默认10个）
- **操作审计**：所有操作都会记录在系统日志中
- **访问控制**：定期检查客户端连接状态

### 3. 数据安全
- **日志保护**：系统日志包含敏感信息，需妥善保管
- **配置安全**：配置文件不应包含明文密码
- **会话管理**：及时清理断开的客户端连接

## 📝 操作规则

### 1. 控制端操作规范

#### 启动规则
- 启动前检查网络环境
- 确认端口8888未被占用
- 验证防火墙设置
- 检查系统资源状态

#### 客户端管理
- 定期检查客户端连接状态
- 及时清理离线客户端
- 记录客户端操作历史
- 监控系统资源使用

#### 命令发送
- 确认目标客户端在线状态
- 验证命令参数正确性
- 记录命令发送历史
- 监控命令执行结果

### 2. 客户端操作规范

#### 连接规则
- 使用正确的服务器地址和端口
- 设置唯一的客户端ID
- 保持网络连接稳定
- 及时响应心跳检测

#### 浏览器操作
- 确保Chrome浏览器可用
- 监控浏览器资源使用
- 及时关闭不需要的浏览器实例
- 报告操作执行结果

### 3. 系统维护规则

#### 日常维护
- 定期检查系统日志
- 清理过期的日志文件
- 监控系统性能指标
- 备份重要配置文件

#### 故障处理
- 记录故障现象和错误信息
- 按优先级处理故障
- 及时通知相关人员
- 建立故障处理流程

## 🚫 禁止行为

### 1. 安全禁止
- ❌ 在公网环境使用系统
- ❌ 使用默认密码或弱密码
- ❌ 共享管理员账户
- ❌ 绕过安全检查机制

### 2. 操作禁止
- ❌ 向未知客户端发送命令
- ❌ 执行未授权的浏览器操作
- ❌ 修改系统核心文件
- ❌ 删除重要日志文件

### 3. 网络禁止
- ❌ 开放不必要的网络端口
- ❌ 使用不安全的网络连接
- ❌ 绕过防火墙设置
- ❌ 在公共WiFi环境使用

## ✅ 推荐做法

### 1. 安全最佳实践
- ✅ 定期更新系统和依赖包
- ✅ 使用强密码和定期更换
- ✅ 启用日志审计功能
- ✅ 定期备份重要数据

### 2. 操作最佳实践
- ✅ 在测试环境验证新功能
- ✅ 记录所有操作步骤
- ✅ 定期检查系统状态
- ✅ 建立操作标准化流程

### 3. 监控最佳实践
- ✅ 设置系统监控告警
- ✅ 定期检查性能指标
- ✅ 监控异常连接行为
- ✅ 建立应急响应机制

## 📊 监控指标

### 1. 系统性能指标
- CPU使用率：< 80%
- 内存使用率：< 85%
- 网络延迟：< 100ms
- 磁盘使用率：< 90%

### 2. 连接状态指标
- 客户端连接数：< 最大限制
- 连接成功率：> 95%
- 心跳响应时间：< 30秒
- 命令执行成功率：> 90%

### 3. 安全指标
- 异常连接尝试：0
- 未授权访问：0
- 系统错误率：< 1%
- 日志完整性：100%

## 🔧 配置规则

### 1. 服务器配置
```json
{
  "server": {
    "host": "0.0.0.0",
    "port": 8888,
    "max_clients": 100,
    "timeout": 30,
    "heartbeat_interval": 5
  }
}
```

### 2. 客户端配置
```json
{
  "client": {
    "default_server_host": "localhost",
    "default_server_port": 8888,
    "heartbeat_interval": 10,
    "reconnect_attempts": 3,
    "reconnect_delay": 5
  }
}
```

### 3. 浏览器配置
```json
{
  "browser": {
    "window_width": 1200,
    "window_height": 800,
    "headless": false,
    "chrome_options": [
      "--no-sandbox",
      "--disable-dev-shm-usage",
      "--disable-gpu"
    ]
  }
}
```

## 📋 操作检查清单

### 启动前检查
- [ ] 网络连接正常
- [ ] 端口8888可用
- [ ] 防火墙配置正确
- [ ] 依赖包已安装
- [ ] Chrome浏览器可用

### 运行中检查
- [ ] 客户端连接状态
- [ ] 系统资源使用
- [ ] 日志文件大小
- [ ] 异常连接监控
- [ ] 命令执行结果

### 关闭前检查
- [ ] 保存重要配置
- [ ] 清理临时文件
- [ ] 关闭所有连接
- [ ] 备份日志文件
- [ ] 记录运行状态

## 🚨 应急响应

### 1. 安全事件响应
1. **立即断开网络连接**
2. **记录事件详情**
3. **分析安全日志**
4. **通知相关人员**
5. **采取补救措施**

### 2. 系统故障响应
1. **停止相关服务**
2. **备份重要数据**
3. **分析故障原因**
4. **修复系统问题**
5. **验证系统恢复**

### 3. 数据丢失响应
1. **停止所有操作**
2. **评估损失范围**
3. **恢复备份数据**
4. **分析丢失原因**
5. **加强防护措施**

## 📞 联系信息

### 技术支持
- **系统管理员**：[管理员联系方式]
- **技术支持**：[技术支持联系方式]
- **安全团队**：[安全团队联系方式]

### 紧急联系
- **紧急事件**：[紧急联系电话]
- **安全事件**：[安全事件联系电话]
- **系统故障**：[故障处理联系电话]

## 📅 规则更新

### 版本历史
- **v1.0.0** (2024-01-01) - 初始版本
- **v1.1.0** (2024-01-15) - 增加安全规则
- **v1.2.0** (2024-02-01) - 完善操作规范

### 更新流程
1. 提出规则修改建议
2. 评估修改影响范围
3. 获得相关方批准
4. 更新规则文档
5. 通知所有用户

---

**重要提醒**：本规则文档是系统使用的重要指导文件，所有用户必须严格遵守。违反规则可能导致系统安全问题或操作故障，请务必认真阅读并执行。 