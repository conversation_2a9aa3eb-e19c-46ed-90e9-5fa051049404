import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import socket
import threading
import json
import uuid
from datetime import datetime
import time

class ControlCenter:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("控制中台 - 服务端")
        self.root.geometry("1200x800")
        
        # 服务器配置
        self.host = '0.0.0.0'
        self.port = 8888
        self.server_socket = None
        self.clients = {}  # {client_id: {'socket': socket, 'ip': ip, 'status': 'online', 'last_seen': time}}
        self.server_running = False
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部控制区域
        control_frame = ttk.LabelFrame(main_frame, text="服务器控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 服务器状态和控制按钮
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill=tk.X)
        
        self.status_label = ttk.Label(status_frame, text="服务器状态: 未启动", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.start_btn = ttk.Button(status_frame, text="启动服务器", command=self.start_server)
        self.start_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        self.stop_btn = ttk.Button(status_frame, text="停止服务器", command=self.stop_server, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 客户端操作区域
        operation_frame = ttk.LabelFrame(main_frame, text="客户端操作", padding=10)
        operation_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 网站输入
        url_frame = ttk.Frame(operation_frame)
        url_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(url_frame, text="目标网站:").pack(side=tk.LEFT)
        self.url_entry = ttk.Entry(url_frame, width=50)
        self.url_entry.pack(side=tk.LEFT, padx=(10, 10))
        self.url_entry.insert(0, "https://www.baidu.com")
        
        # 操作按钮
        btn_frame = ttk.Frame(operation_frame)
        btn_frame.pack(fill=tk.X)
        
        self.open_browser_btn = ttk.Button(btn_frame, text="打开浏览器访问网站", 
                                         command=self.open_browser_command, state=tk.DISABLED)
        self.open_browser_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.refresh_btn = ttk.Button(btn_frame, text="刷新客户端状态", 
                                    command=self.refresh_clients, state=tk.DISABLED)
        self.refresh_btn.pack(side=tk.LEFT)
        
        # 客户端列表区域
        client_frame = ttk.LabelFrame(main_frame, text="客户端列表", padding=10)
        client_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ('客户端ID', 'IP地址', '状态', '最后在线时间', '选择')
        self.client_tree = ttk.Treeview(client_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题
        for col in columns:
            self.client_tree.heading(col, text=col)
            self.client_tree.column(col, width=150)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(client_frame, orient=tk.VERTICAL, command=self.client_tree.yview)
        self.client_tree.configure(yscrollcommand=scrollbar.set)
        
        self.client_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="系统日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
    def start_server(self):
        """启动服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            
            self.server_running = True
            self.status_label.config(text="服务器状态: 运行中", foreground="green")
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            self.open_browser_btn.config(state=tk.NORMAL)
            self.refresh_btn.config(state=tk.NORMAL)
            
            self.log_message("服务器启动成功，监听端口 8888")
            
            # 启动监听线程
            listen_thread = threading.Thread(target=self.listen_for_clients, daemon=True)
            listen_thread.start()
            
            # 启动客户端状态监控线程
            monitor_thread = threading.Thread(target=self.monitor_clients, daemon=True)
            monitor_thread.start()
            
        except Exception as e:
            self.log_message(f"服务器启动失败: {str(e)}")
            messagebox.showerror("错误", f"服务器启动失败: {str(e)}")
            
    def stop_server(self):
        """停止服务器"""
        self.server_running = False
        
        # 断开所有客户端连接
        for client_id, client_info in self.clients.items():
            try:
                client_info['socket'].close()
            except:
                pass
        self.clients.clear()
        
        # 关闭服务器socket
        if self.server_socket:
            self.server_socket.close()
            
        self.status_label.config(text="服务器状态: 已停止", foreground="red")
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.open_browser_btn.config(state=tk.DISABLED)
        self.refresh_btn.config(state=tk.DISABLED)
        
        # 清空客户端列表
        for item in self.client_tree.get_children():
            self.client_tree.delete(item)
            
        self.log_message("服务器已停止")
        
    def listen_for_clients(self):
        """监听客户端连接"""
        while self.server_running:
            try:
                client_socket, address = self.server_socket.accept()
                client_thread = threading.Thread(target=self.handle_client, 
                                               args=(client_socket, address), daemon=True)
                client_thread.start()
            except:
                if self.server_running:
                    break
                    
    def handle_client(self, client_socket, address):
        """处理客户端连接"""
        try:
            # 接收客户端注册信息
            data = client_socket.recv(1024).decode('utf-8')
            client_info = json.loads(data)
            
            client_id = client_info.get('client_id', str(uuid.uuid4())[:8])
            ip_address = address[0]
            
            # 保存客户端信息
            self.clients[client_id] = {
                'socket': client_socket,
                'ip': ip_address,
                'status': 'online',
                'last_seen': time.time(),
                'client_name': client_info.get('client_name', f'Client-{client_id}')
            }
            
            self.log_message(f"客户端 {client_id} ({ip_address}) 已连接")
            self.update_client_list()
            
            # 发送确认消息
            response = {
                'type': 'register_confirm',
                'client_id': client_id,
                'status': 'success'
            }
            client_socket.send(json.dumps(response).encode('utf-8'))
            
            # 保持连接，接收客户端消息
            while self.server_running:
                try:
                    data = client_socket.recv(1024).decode('utf-8')
                    if not data:
                        break
                    
                    message = json.loads(data)
                    self.handle_client_message(client_id, message)
                    
                except:
                    break
                    
        except Exception as e:
            self.log_message(f"处理客户端连接时出错: {str(e)}")
        finally:
            # 客户端断开连接
            if client_id in self.clients:
                del self.clients[client_id]
                self.log_message(f"客户端 {client_id} 已断开连接")
                self.update_client_list()
            try:
                client_socket.close()
            except:
                pass
                
    def handle_client_message(self, client_id, message):
        """处理客户端消息"""
        msg_type = message.get('type')
        
        if msg_type == 'status_update':
            # 更新客户端状态
            if client_id in self.clients:
                self.clients[client_id]['status'] = message.get('status', 'online')
                self.clients[client_id]['last_seen'] = time.time()
                self.update_client_list()
                
        elif msg_type == 'operation_result':
            # 操作结果反馈
            operation = message.get('operation')
            success = message.get('success', False)
            result_msg = message.get('message', '')
            
            status = "成功" if success else "失败"
            self.log_message(f"客户端 {client_id} 执行 {operation}: {status} - {result_msg}")
            
    def monitor_clients(self):
        """监控客户端状态"""
        while self.server_running:
            current_time = time.time()
            offline_clients = []
            
            for client_id, client_info in self.clients.items():
                # 检查客户端是否超时（30秒无响应）
                if current_time - client_info['last_seen'] > 30:
                    client_info['status'] = 'offline'
                    offline_clients.append(client_id)
                    
            # 标记离线客户端
            for client_id in offline_clients:
                if client_id in self.clients:
                    self.clients[client_id]['status'] = 'offline'
                    
            if offline_clients:
                self.update_client_list()
                
            time.sleep(5)  # 每5秒检查一次
            
    def update_client_list(self):
        """更新客户端列表显示"""
        # 清空现有列表
        for item in self.client_tree.get_children():
            self.client_tree.delete(item)
            
        # 添加客户端信息
        for client_id, client_info in self.clients.items():
            status = client_info['status']
            last_seen = datetime.fromtimestamp(client_info['last_seen']).strftime("%H:%M:%S")
            
            # 设置状态颜色
            status_color = "green" if status == "online" else "red"
            
            item = self.client_tree.insert('', 'end', values=(
                client_id,
                client_info['ip'],
                status,
                last_seen,
                "□"  # 复选框占位符
            ))
            
            # 设置状态列的颜色
            self.client_tree.set(item, '状态', status)
            
    def refresh_clients(self):
        """刷新客户端状态"""
        self.update_client_list()
        self.log_message("客户端状态已刷新")
        
    def open_browser_command(self):
        """发送打开浏览器命令"""
        selected_items = self.client_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要控制的客户端")
            return
            
        url = self.url_entry.get().strip()
        if not url:
            messagebox.showwarning("警告", "请输入要访问的网站地址")
            return
            
        # 确保URL格式正确
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            
        success_count = 0
        failed_clients = []
        
        for item in selected_items:
            values = self.client_tree.item(item)['values']
            client_id = values[0]
            
            if client_id in self.clients:
                client_info = self.clients[client_id]
                
                if client_info['status'] == 'online':
                    try:
                        command = {
                            'type': 'command',
                            'operation': 'open_browser',
                            'url': url
                        }
                        client_info['socket'].send(json.dumps(command).encode('utf-8'))
                        success_count += 1
                        self.log_message(f"已向客户端 {client_id} 发送打开浏览器命令")
                    except Exception as e:
                        failed_clients.append(client_id)
                        self.log_message(f"向客户端 {client_id} 发送命令失败: {str(e)}")
                else:
                    failed_clients.append(client_id)
                    self.log_message(f"客户端 {client_id} 离线，无法发送命令")
                    
        if success_count > 0:
            messagebox.showinfo("成功", f"已向 {success_count} 个客户端发送命令")
        if failed_clients:
            messagebox.showwarning("警告", f"以下客户端发送失败: {', '.join(failed_clients)}")
            
    def run(self):
        """运行控制中心"""
        self.root.mainloop()

if __name__ == "__main__":
    control_center = ControlCenter()
    control_center.run() 